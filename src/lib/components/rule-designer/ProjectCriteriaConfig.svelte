<script lang="ts">
  import { onMount } from 'svelte';
  import { Button } from '$lib/components/ui/button';
  import * as Dialog from '$lib/components/ui/dialog';
  import { ruleDesignerService, type ProjectCriterionWithRule, type CreateProjectCriterionRequest } from '$lib/services/ruleDesignerService';
  import { default as ProjectCriterionForm } from './ProjectCriterionForm.svelte';
  import { Edit, Trash2, Plus, ArrowUp, ArrowDown, Upload, AlertCircle, CheckCircle2, Download, Trash, CheckSquare } from 'lucide-svelte';
  import criteriaImportTemplate from '$lib/examples/criteria_import_template.json';



  // Props
  const { projectId } = $props<{ projectId: string }>();

  // 状态管理
  let isLoading = $state(true);
  let error = $state<string | null>(null);
  let inclusionCriteria = $state<ProjectCriterionWithRule[]>([]);
  let exclusionCriteria = $state<ProjectCriterionWithRule[]>([]);
  let activeTab = $state('inclusion');
  let selectedCriteria = $state<ProjectCriterionWithRule[]>([]);

  // 对话框状态
  let showAddDialog = $state(false);
  let showEditDialog = $state(false);
  let showDeleteDialog = $state(false);
  let showBatchDeleteDialog = $state(false);
  let showGroupDialog = $state(false);
  let showUngroupDialog = $state(false);
  let showImportDialog = $state(false);
  let showImportResultDialog = $state(false);
  let selectedCriterion = $state<ProjectCriterionWithRule | null>(null);

  // 导入相关状态
  let importError = $state<string | null>(null);
  let importInProgress = $state(false);
  let jsonPasteContent = $state('');
  let importResults = $state<{
    total: number;
    success: number;
    failed: number;
    details: string[];
  }>({
    total: 0,
    success: 0,
    failed: 0,
    details: []
  });

  // 加载项目标准
  async function loadProjectCriteria() {
    isLoading = true;
    error = null;

    try {
      // 确保规则设计器表已初始化
      await ruleDesignerService.initTables();

      // 加载入组标准
      const inclusionQuery = { project_id: projectId, criterion_type: 'inclusion' };

      try {
        const inclusionResult = await ruleDesignerService.getProjectCriteria(inclusionQuery);

        if (Array.isArray(inclusionResult)) {
          inclusionCriteria = [...inclusionResult];
        } else {
          console.error('入组标准数据格式错误');
          inclusionCriteria = [];
        }
      } catch (inclusionErr: any) {
        error = `加载入组标准失败: ${inclusionErr.message || '未知错误'}`;
        inclusionCriteria = [];
      }

      // 加载排除标准
      const exclusionQuery = { project_id: projectId, criterion_type: 'exclusion' };

      try {
        const exclusionResult = await ruleDesignerService.getProjectCriteria(exclusionQuery);

        if (Array.isArray(exclusionResult)) {
          exclusionCriteria = [...exclusionResult];
        } else {
          console.error('排除标准数据格式错误');
          exclusionCriteria = [];
        }
      } catch (exclusionErr: any) {
        if (!error) { // 只有在入组标准没有错误时才设置
          error = `加载排除标准失败: ${exclusionErr.message || '未知错误'}`;
        }
        exclusionCriteria = [];
      }
    } catch (err: any) {
      error = `加载项目标准失败: ${err.message || '未知错误'}`;
      inclusionCriteria = [];
      exclusionCriteria = [];
    } finally {
      isLoading = false;
    }
  }

  // 添加标准
  function addCriterion() {
    showAddDialog = true;
  }

  // 编辑标准
  function editCriterion(criterion: ProjectCriterionWithRule) {
    selectedCriterion = criterion;
    showEditDialog = true;
  }

  // 确认删除标准
  function confirmDeleteCriterion(criterion: ProjectCriterionWithRule) {
    selectedCriterion = criterion;
    showDeleteDialog = true;
  }

  // 执行删除
  async function deleteCriterion() {
    if (!selectedCriterion || !selectedCriterion.criterion.project_criterion_id) {
      return;
    }

    try {
      await ruleDesignerService.deleteProjectCriterion(selectedCriterion.criterion.project_criterion_id);

      // 关闭对话框
      showDeleteDialog = false;

      // 重置选中的标准
      selectedCriterion = null;

      // 重新加载数据
      await loadProjectCriteria();
    } catch (err: any) {
      error = `删除项目标准失败: ${err.message || '未知错误'}`;
      console.error('删除项目标准失败:', err);
    }
  }

  // 显示批量删除对话框
  function showBatchDeleteConfirmation() {
    if (selectedCriteria.length === 0) {
      error = "请先选择要删除的标准";
      return;
    }
    showBatchDeleteDialog = true;
  }

  // 在批量删除对话框中切换所有选中项
  function toggleAllInBatchDeleteDialog() {
    // 获取当前显示的标准
    const currentCriteria = activeTab === 'inclusion' ? inclusionCriteria : exclusionCriteria;

    // 检查是否所有当前标签页的标准都已选中
    const allSelected = currentCriteria.length > 0 &&
                        currentCriteria.every(c => selectedCriteria.some(
                          sc => sc.criterion.project_criterion_id === c.criterion.project_criterion_id
                        ));

    if (allSelected) {
      // 如果全部选中，则取消选择当前标签页的所有标准
      selectedCriteria = selectedCriteria.filter(c =>
        c.criterion.criterion_type !== activeTab
      );
    } else {
      // 如果未全部选中，则选择当前标签页的所有标准
      // 先移除当前标签页中已选择的标准，避免重复
      const otherTabSelected = selectedCriteria.filter(c =>
        c.criterion.criterion_type !== activeTab
      );

      // 然后添加当前标签页中的所有标准
      selectedCriteria = [...otherTabSelected, ...currentCriteria];
    }
  }

  // 执行批量删除
  async function batchDeleteCriteria() {
    if (selectedCriteria.length === 0) {
      return;
    }

    try {
      let successCount = 0;
      let failCount = 0;

      // 逐个删除选中的标准
      for (const criterion of selectedCriteria) {
        if (!criterion.criterion.project_criterion_id) continue;

        try {
          await ruleDesignerService.deleteProjectCriterion(criterion.criterion.project_criterion_id);
          successCount++;
        } catch (err) {
          failCount++;
          console.error(`删除标准 ${criterion.rule_definition.rule_name} 失败:`, err);
        }
      }

      // 关闭对话框
      showBatchDeleteDialog = false;

      // 重置选中的标准
      selectedCriteria = [];

      // 显示结果消息
      if (failCount > 0) {
        error = `批量删除完成: ${successCount} 个成功, ${failCount} 个失败`;
      } else {
        error = null; // 清除之前的错误消息
      }

      // 重新加载数据
      await loadProjectCriteria();
    } catch (err: any) {
      error = `批量删除失败: ${err.message || '未知错误'}`;
      console.error('批量删除失败:', err);
    }
  }

  // 移动标准顺序
  async function moveCriterion(criterion: ProjectCriterionWithRule, direction: 'up' | 'down') {
    if (!criterion.criterion.project_criterion_id) return;

    const criteria = criterion.criterion.criterion_type === 'inclusion' ? inclusionCriteria : exclusionCriteria;
    const index = criteria.findIndex(c => c.criterion.project_criterion_id === criterion.criterion.project_criterion_id);

    if (index === -1) return;

    if (direction === 'up' && index === 0) return;
    if (direction === 'down' && index === criteria.length - 1) return;

    const newIndex = direction === 'up' ? index - 1 : index + 1;
    const targetCriterion = criteria[newIndex];

    // 交换显示顺序
    const tempOrder = criterion.criterion.display_order;

    try {
      // 更新当前标准的显示顺序
      await ruleDesignerService.updateProjectCriterion(
        criterion.criterion.project_criterion_id,
        { display_order: targetCriterion.criterion.display_order }
      );

      // 更新目标标准的显示顺序
      await ruleDesignerService.updateProjectCriterion(
        targetCriterion.criterion.project_criterion_id!,
        { display_order: tempOrder }
      );

      // 重新加载标准
      loadProjectCriteria();
    } catch (err: any) {
      error = err.message || '移动项目标准失败';
      console.error('移动项目标准失败:', err);
    }
  }

  // 处理对话框关闭
  function handleDialogClose() {
    // 重新加载项目标准
    loadProjectCriteria();

    // 重置选中的标准
    if (showEditDialog) {
      selectedCriterion = null;
      showEditDialog = false;
    }

    if (showAddDialog) {
      showAddDialog = false;
    }

    if (showDeleteDialog) {
      selectedCriterion = null;
      showDeleteDialog = false;
    }

    if (showBatchDeleteDialog) {
      showBatchDeleteDialog = false;
    }

    if (showGroupDialog) {
      selectedCriteria = [];
      showGroupDialog = false;
    }

    if (showUngroupDialog) {
      selectedCriteria = [];
      showUngroupDialog = false;
    }

    if (showImportDialog) {
      importError = null;
      showImportDialog = false;
    }

    if (showImportResultDialog) {
      importResults = {
        total: 0,
        success: 0,
        failed: 0,
        details: []
      };
      showImportResultDialog = false;
    }
  }

  // 显示导入对话框
  function showImportJsonDialog() {
    importError = null;
    showImportDialog = true;
  }

  // 下载模板文件
  function downloadTemplateFile(type: 'simple' | 'detailed' = 'simple') {
    let filename;

    if (type === 'simple') {
      // 简单模板 - 只包含基本的导入格式
      const jsonString = JSON.stringify(criteriaImportTemplate, null, 2);
      filename = 'criteria_import_template.json';
      downloadFile(jsonString, filename);
    } else {
      // 详细模板 - 包含规则定义、参数说明和完整示例
      import('$lib/examples/criteria_import_with_rules.json').then((module) => {
        const detailedData = module.default;

        // 创建一个说明文档，解释如何使用详细模板
        const usageGuide = {
          "说明": {
            "使用方法": "本文件包含规则定义和示例，可用于参考。实际导入时，只需使用'criteria'和'or_groups'部分。",
            "规则分类": "系统会根据rule_definition_id自动查询规则的category字段，判断是入组还是排除标准。",
            "参数类型": "请根据规则定义中的parameter_schema填写正确类型的参数值。"
          },
          ...detailedData
        };

        const jsonString = JSON.stringify(usageGuide, null, 2);
        downloadFile(jsonString, 'criteria_import_detailed_guide.json');
      }).catch(err => {
        console.error('加载详细模板失败:', err);
        // 如果加载失败，回退到简单模板
        const jsonString = JSON.stringify(criteriaImportTemplate, null, 2);
        downloadFile(jsonString, 'criteria_import_template.json');
        alert('加载详细模板失败，已下载简单模板');
      });
    }
  }

  // 通用下载文件函数
  function downloadFile(content: string, filename: string) {
    const blob = new Blob([content], { type: 'application/json' });
    const url = URL.createObjectURL(blob);

    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();

    // 清理
    setTimeout(() => {
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    }, 0);
  }

  // 导入剪贴板服务
  import { setClipboardText } from '$lib/services/clipboardService';

  // 复制示例JSON到剪贴板
  async function copyExampleToClipboard() {
    try {
      // 获取示例JSON内容 - 使用新格式
      const exampleJson = {
        criteria: [
          {
            rule_definition_id: 4,
            parameter_values: {
              Min_age: 40,
              Max_age: 80
            },
            display_order: 1
          },
          {
            rule_definition_id: 7,
            parameter_values: {
              diagnose_month: 12
            },
            display_order: 2
          },
          {
            rule_definition_id: 13,
            parameter_values: {
              Max_number: 0.7
            },
            display_order: 3
          },
          {
            rule_definition_id: 12,
            parameter_values: {
              Min_number: 2
            },
            display_order: 4
          },
          {
            rule_definition_id: 14,
            parameter_values: {
              Type: "闭合三联吸入维持治疗（吸入性糖皮质激素（ICS）+长效β-2受体激动剂（LABA）+长效毒蕈碱受体拮抗剂（LAMA））",
              Maintenance_duration: 3,
              Stable_dosage_duration: 1
            },
            display_order: 5
          },
          {
            rule_definition_id: 17,
            parameter_values: {
              date_number: 12,
              Min_count: 2,
              degree: "中度"
            },
            display_order: 6
          },
          {
            rule_definition_id: 17,
            parameter_values: {
              date_number: 12,
              Min_count: 1,
              degree: "重度"
            },
            display_order: 7
          }
        ],
        or_groups: [
          {
            group_id: 1,
            criteria_ids: [6, 7],  // 这里的数字对应上面criteria数组中的索引（从1开始），例如6表示第6个criteria项
            operator: "OR"
          }
        ]
      };

      // 格式化JSON
      const jsonString = JSON.stringify(exampleJson, null, 2);

      // 使用剪贴板服务复制到剪贴板
      await setClipboardText(jsonString);

      // 显示成功提示
      const button = document.querySelector('button[title^="复制到剪贴板"]');
      if (button) {
        const originalText = button.textContent;
        button.textContent = '已复制!';
        button.classList.add('bg-green-100', 'text-green-700', 'dark:bg-green-900', 'dark:text-green-300');

        // 3秒后恢复原样
        setTimeout(() => {
          button.textContent = originalText;
          button.classList.remove('bg-green-100', 'text-green-700', 'dark:bg-green-900', 'dark:text-green-300');
        }, 3000);
      }

      // 自动填充到文本区域
      jsonPasteContent = jsonString;
    } catch (err) {
      console.error('复制到剪贴板失败:', err);
      alert('复制到剪贴板失败，请手动复制。');
    }
  }

  // 处理JSON文件导入
  async function handleJsonImport(event: Event) {
    const input = event.target as HTMLInputElement;
    if (!input.files || input.files.length === 0) {
      importError = "请选择一个JSON文件";
      return;
    }

    const file = input.files[0];
    if (file.type !== "application/json" && !file.name.endsWith('.json')) {
      importError = "请选择有效的JSON文件";
      return;
    }

    importInProgress = true;
    importError = null;
    importResults = {
      total: 0,
      success: 0,
      failed: 0,
      details: []
    };

    try {
      // 读取文件内容
      const fileContent = await file.text();
      await processJsonContent(fileContent);

      // 重置文件输入
      input.value = '';
    } catch (err: any) {
      importError = `导入失败: ${err.message || '未知错误'}`;
      console.error('导入JSON失败:', err);
      importInProgress = false;
    }
  }

  // 处理粘贴的JSON代码
  async function handlePastedJsonImport() {
    if (!jsonPasteContent.trim()) {
      importError = "请先粘贴JSON代码";
      return;
    }

    importInProgress = true;
    importError = null;
    importResults = {
      total: 0,
      success: 0,
      failed: 0,
      details: []
    };

    try {
      await processJsonContent(jsonPasteContent.trim());

      // 清空文本区域
      jsonPasteContent = '';
    } catch (err: any) {
      importError = `导入失败: ${err.message || '未知错误'}`;
      console.error('导入JSON失败:', err);
      importInProgress = false;
    }
  }

  // 处理JSON内容（文件或粘贴）
  async function processJsonContent(content: string) {
    let jsonData;

    try {
      jsonData = JSON.parse(content);
    } catch (error: any) {
      importError = `JSON解析错误: ${error.message || '格式不正确'}`;
      console.error('JSON解析错误:', error);
      importInProgress = false;
      return;
    }

    // 验证JSON结构
    if (!validateImportJson(jsonData)) {
      importError = "JSON格式无效，请检查文件结构";
      importInProgress = false;
      return;
    }

    // 处理导入
    await processImportJson(jsonData);

    // 关闭导入对话框，显示结果对话框
    showImportDialog = false;
    showImportResultDialog = true;

    // 重新加载标准列表
    await loadProjectCriteria();

    importInProgress = false;
  }



  // 验证导入的JSON结构
  function validateImportJson(jsonData: any): boolean {
    // 检查基本结构
    if (typeof jsonData !== 'object' || jsonData === null) {
      importResults.details.push("JSON必须是一个对象");
      return false;
    }

    // 检查新格式（criteria数组）或旧格式（inclusion/exclusion数组）
    const hasNewFormat = Array.isArray(jsonData.criteria);
    const hasOldFormat = Array.isArray(jsonData.inclusion) || Array.isArray(jsonData.exclusion);

    if (!hasNewFormat && !hasOldFormat) {
      importResults.details.push("JSON必须包含'criteria'数组或'inclusion'/'exclusion'数组");
      return false;
    }

    // 检查每个标准的必要字段
    let isValid = true;

    // 检查新格式
    if (hasNewFormat) {
      jsonData.criteria.forEach((criterion: any, index: number) => {
        if (!validateCriterion(criterion, 'criteria', index)) {
          isValid = false;
        }
      });
    }

    // 检查旧格式
    if (Array.isArray(jsonData.inclusion)) {
      jsonData.inclusion.forEach((criterion: any, index: number) => {
        if (!validateCriterion(criterion, 'inclusion', index)) {
          isValid = false;
        }
      });
    }

    if (Array.isArray(jsonData.exclusion)) {
      jsonData.exclusion.forEach((criterion: any, index: number) => {
        if (!validateCriterion(criterion, 'exclusion', index)) {
          isValid = false;
        }
      });
    }

    // 检查or_groups结构（如果存在）
    if (jsonData.or_groups && Array.isArray(jsonData.or_groups)) {
      jsonData.or_groups.forEach((group: any, index: number) => {
        if (!validateOrGroup(group, index, jsonData)) {
          isValid = false;
        }
      });
    }

    return isValid;
  }

  // 验证单个标准
  function validateCriterion(criterion: any, type: string, index: number): boolean {
    if (typeof criterion !== 'object' || criterion === null) {
      importResults.details.push(`${type}[${index}]: 必须是一个对象`);
      return false;
    }

    if (!criterion.rule_definition_id || typeof criterion.rule_definition_id !== 'number') {
      importResults.details.push(`${type}[${index}]: 缺少有效的rule_definition_id`);
      return false;
    }

    if (!criterion.parameter_values || typeof criterion.parameter_values !== 'object') {
      importResults.details.push(`${type}[${index}]: 缺少有效的parameter_values对象`);
      return false;
    }

    return true;
  }

  // 验证OR组
  function validateOrGroup(group: any, index: number, jsonData: any): boolean {
    if (typeof group !== 'object' || group === null) {
      importResults.details.push(`or_groups[${index}]: 必须是一个对象`);
      return false;
    }

    // 新格式不需要type字段，直接使用criteria_indices或criteria_ids
    const hasNewFormat = Array.isArray(jsonData.criteria);
    const hasCriteriaIndices = Array.isArray(group.criteria_indices);
    const hasCriteriaIds = Array.isArray(group.criteria_ids);

    // 检查旧格式的type字段
    if (!hasNewFormat && (!group.type || (group.type !== 'inclusion' && group.type !== 'exclusion'))) {
      importResults.details.push(`or_groups[${index}]: 使用旧格式时，type必须是'inclusion'或'exclusion'`);
      return false;
    }

    // 检查是否有criteria_indices或criteria_ids
    if (!hasCriteriaIndices && !hasCriteriaIds) {
      importResults.details.push(`or_groups[${index}]: 必须包含'criteria_indices'或'criteria_ids'数组`);
      return false;
    }

    // 检查criteria_indices
    if (hasCriteriaIndices && group.criteria_indices.length < 2) {
      importResults.details.push(`or_groups[${index}]: criteria_indices必须至少包含2个元素`);
      return false;
    }

    // 检查criteria_ids
    if (hasCriteriaIds && group.criteria_ids.length < 2) {
      importResults.details.push(`or_groups[${index}]: criteria_ids必须至少包含2个元素`);
      return false;
    }

    // 如果使用criteria_indices，检查索引是否有效
    if (hasCriteriaIndices) {
      const sourceArray = hasNewFormat ? jsonData.criteria : jsonData[group.type];
      if (!Array.isArray(sourceArray)) {
        const arrayName = hasNewFormat ? 'criteria' : group.type;
        importResults.details.push(`or_groups[${index}]: 引用的${arrayName}数组不存在`);
        return false;
      }

      for (const criterionIndex of group.criteria_indices) {
        if (typeof criterionIndex !== 'number' || criterionIndex < 0 || criterionIndex >= sourceArray.length) {
          const arrayName = hasNewFormat ? 'criteria' : group.type;
          importResults.details.push(`or_groups[${index}]: 索引${criterionIndex}超出${arrayName}数组范围`);
          return false;
        }
      }
    }

    // 如果使用criteria_ids，检查是否为数字数组
    if (hasCriteriaIds) {
      for (const criterionId of group.criteria_ids) {
        if (typeof criterionId !== 'number') {
          importResults.details.push(`or_groups[${index}]: criteria_ids中的元素必须是数字`);
          return false;
        }
      }
    }

    // 检查operator字段（如果存在）
    if (group.operator && group.operator !== 'OR') {
      importResults.details.push(`or_groups[${index}]: operator字段必须是'OR'`);
      return false;
    }

    return true;
  }

  // 处理导入的JSON数据
  async function processImportJson(jsonData: any) {
    // 创建一个映射，用于跟踪每个导入标准的ID
    const criteriaMap: Record<string, Record<number, number>> = {
      criteria: {},      // 索引 -> 数据库ID
      inclusion: {},     // 索引 -> 数据库ID
      exclusion: {},     // 索引 -> 数据库ID
      displayOrder: {}   // display_order值 -> 数据库ID
    };

    // 存储需要创建的"或"关系组信息
    const orGroupsToCreate: any[] = [];

    // 处理新格式（criteria数组）
    if (Array.isArray(jsonData.criteria)) {
      importResults.total += jsonData.criteria.length;

      // 第一步：创建所有标准
      console.log('第一步：创建所有标准...');
      for (let i = 0; i < jsonData.criteria.length; i++) {
        const criterion = jsonData.criteria[i];
        try {
          // 获取规则定义
          const ruleDefinition = await ruleDesignerService.getRuleDefinitionById(criterion.rule_definition_id);

          if (!ruleDefinition) {
            throw new Error(`找不到规则定义 (ID: ${criterion.rule_definition_id})`);
          }

          // 根据规则定义的category字段确定标准类型
          const criterionType = ruleDefinition.category === '排除标准' ? 'exclusion' : 'inclusion';

          // 创建标准
          const result = await createCriterion(criterion, criterionType);
          const dbId = result.criterion.project_criterion_id!;

          // 保存索引到数据库ID的映射
          criteriaMap.criteria[i] = dbId;

          // 如果有display_order，也保存display_order到数据库ID的映射
          if (criterion.display_order !== undefined) {
            criteriaMap.displayOrder[criterion.display_order] = dbId;
          }

          importResults.success++;

          const typeText = criterionType === 'inclusion' ? '入组标准' : '排除标准';
          importResults.details.push(`成功导入${typeText}: ${result.rule_definition.rule_name}`);
        } catch (err: any) {
          importResults.failed++;
          importResults.details.push(`导入标准失败 [${i}]: ${err.message || '未知错误'}`);
        }
      }

      // 第二步：等待所有标准创建完成
      console.log('第二步：等待所有标准创建完成...');
      console.log('等待2秒，确保所有标准都已经在数据库中创建完成...');
      await new Promise(resolve => setTimeout(resolve, 2000));

      // 第三步：重新加载所有标准，确保我们有最新的数据
      console.log('第三步：重新加载所有标准...');
      await loadProjectCriteria();
      console.log('重新加载完成，入组标准数量:', inclusionCriteria.length, '排除标准数量:', exclusionCriteria.length);

      // 第四步：收集需要创建的"或"关系组信息
      console.log('第四步：收集需要创建的"或"关系组信息...');
      if (Array.isArray(jsonData.or_groups)) {
        for (const group of jsonData.or_groups) {
          try {
            let criteriaToGroup = [];
            let criteriaIdsInfo = [];

            if (Array.isArray(group.criteria_ids)) {
              // criteria_ids是指criteria数组中的索引（从1开始）
              for (const criteriaIndex of group.criteria_ids) {
                // 找到对应索引的criteria项
                const criteriaItem = jsonData.criteria[criteriaIndex - 1]; // 转换为0-based索引

                if (!criteriaItem) {
                  console.warn(`找不到索引为${criteriaIndex}的criteria项，跳过`);
                  continue;
                }

                // 获取该criteria项的display_order和rule_definition_id
                const displayOrder = criteriaItem.display_order;
                const ruleDefinitionId = criteriaItem.rule_definition_id;

                // 在重新加载的标准中查找匹配的标准
                const allCriteria = [...inclusionCriteria, ...exclusionCriteria];
                const matchingCriteria = allCriteria.filter(c =>
                  c.criterion.display_order === displayOrder &&
                  c.rule_definition.rule_definition_id === ruleDefinitionId
                );

                if (matchingCriteria.length > 0) {
                  // 找到匹配的标准
                  const criterion = matchingCriteria[0];
                  criteriaToGroup.push(criterion);
                  criteriaIdsInfo.push(`索引${criteriaIndex}→display_order${displayOrder}→rule_definition_id${ruleDefinitionId}→数据库ID${criterion.criterion.project_criterion_id}`);
                } else {
                  console.warn(`找不到匹配的标准: display_order=${displayOrder}, rule_definition_id=${ruleDefinitionId}`);
                }
              }
            }

            // 如果找到了至少两个标准，则创建一个组
            if (criteriaToGroup.length >= 2) {
              orGroupsToCreate.push({
                criteria: criteriaToGroup,
                info: criteriaIdsInfo
              });
              importResults.details.push(`准备创建"或"关系组: [${criteriaIdsInfo.join(', ')}]`);
            } else {
              importResults.details.push(`无法创建"或"关系组: 找不到足够的匹配标准`);
            }
          } catch (err: any) {
            importResults.details.push(`准备"或"关系组失败: ${err.message || '未知错误'}`);
            console.error('准备"或"关系组失败:', err);
          }
        }
      }

      // 第五步：创建所有"或"关系组
      console.log('第五步：创建所有"或"关系组...');
      for (const groupInfo of orGroupsToCreate) {
        try {
          // 生成一个唯一的组ID
          const groupId = crypto.randomUUID();
          const criteria = groupInfo.criteria;

          // 更新所有标准，设置组ID和操作符
          for (const criterion of criteria) {
            if (!criterion.criterion.project_criterion_id) continue;

            await ruleDesignerService.updateProjectCriterion(
              criterion.criterion.project_criterion_id,
              {
                criteria_group_id: groupId,
                group_operator: 'OR'
              }
            );
          }

          importResults.details.push(`成功创建"或"关系组: [${groupInfo.info.join(', ')}]`);
        } catch (err: any) {
          importResults.details.push(`创建"或"关系组失败: ${err.message || '未知错误'}`);
          console.error('创建"或"关系组失败:', err);
        }
      }

      // 第六步：最后再次重新加载所有标准，确保UI显示正确
      console.log('第六步：最后再次重新加载所有标准...');
      await loadProjectCriteria();

      return; // 处理完新格式后直接返回
    }

    // 处理旧格式（inclusion/exclusion数组）
    console.log('处理旧格式（inclusion/exclusion数组）...');

    // 第一步：创建所有标准
    console.log('第一步：创建所有标准...');

    // 处理inclusion标准
    if (Array.isArray(jsonData.inclusion)) {
      importResults.total += jsonData.inclusion.length;

      for (let i = 0; i < jsonData.inclusion.length; i++) {
        const criterion = jsonData.inclusion[i];
        try {
          const result = await createCriterion(criterion, 'inclusion');
          criteriaMap.inclusion[i] = result.criterion.project_criterion_id!;
          importResults.success++;
          importResults.details.push(`成功导入入组标准: ${result.rule_definition.rule_name}`);
        } catch (err: any) {
          importResults.failed++;
          importResults.details.push(`导入入组标准失败 [${i}]: ${err.message || '未知错误'}`);
        }
      }
    }

    // 处理exclusion标准
    if (Array.isArray(jsonData.exclusion)) {
      importResults.total += jsonData.exclusion.length;

      for (let i = 0; i < jsonData.exclusion.length; i++) {
        const criterion = jsonData.exclusion[i];
        try {
          const result = await createCriterion(criterion, 'exclusion');
          criteriaMap.exclusion[i] = result.criterion.project_criterion_id!;
          importResults.success++;
          importResults.details.push(`成功导入排除标准: ${result.rule_definition.rule_name}`);
        } catch (err: any) {
          importResults.failed++;
          importResults.details.push(`导入排除标准失败 [${i}]: ${err.message || '未知错误'}`);
        }
      }
    }

    // 第二步：等待所有标准创建完成
    console.log('第二步：等待所有标准创建完成...');
    console.log('等待2秒，确保所有标准都已经在数据库中创建完成...');
    await new Promise(resolve => setTimeout(resolve, 2000));

    // 第三步：重新加载所有标准，确保我们有最新的数据
    console.log('第三步：重新加载所有标准...');
    await loadProjectCriteria();
    console.log('重新加载完成，入组标准数量:', inclusionCriteria.length, '排除标准数量:', exclusionCriteria.length);

    // 第四步：处理OR组（旧格式）
    console.log('第四步：处理OR组（旧格式）...');
    if (Array.isArray(jsonData.or_groups)) {
      for (const group of jsonData.or_groups) {
        try {
          await processOldOrGroup(group, criteriaMap);
          importResults.details.push(`成功创建"或"关系组: ${group.type} [${group.criteria_indices.join(', ')}]`);
        } catch (err: any) {
          importResults.details.push(`创建"或"关系组失败: ${err.message || '未知错误'}`);
          console.error('创建"或"关系组失败:', err);
        }
      }
    }

    // 第五步：最后再次重新加载所有标准，确保UI显示正确
    console.log('第五步：最后再次重新加载所有标准...');
    await loadProjectCriteria();
  }

  // 注意：我们不再使用processNewOrGroup函数，而是使用了一个新的方法来创建"或"关系组
  // 新方法在processImportJson函数中实现，它会：
  // 1. 先创建所有标准
  // 2. 重新加载所有标准，确保我们有最新的数据
  // 3. 使用重新加载的标准来创建"或"关系组
  // 这样可以避免使用可能无效的数据库ID

  // 处理旧格式的OR组
  async function processOldOrGroup(group: any, criteriaMap: Record<string, Record<number, number>>) {
    const type = group.type as 'inclusion' | 'exclusion';
    const criteriaToGroup: any[] = [];
    const criteriaIdsInfo: string[] = [];

    // 获取所有标准的ID
    for (const index of group.criteria_indices) {
      const criterionId = criteriaMap[type][index];
      if (!criterionId) {
        throw new Error(`无法找到${type}标准索引${index}的ID`);
      }

      // 重新加载标准，确保我们有最新的数据
      try {
        const criterion = await ruleDesignerService.getProjectCriterionById(criterionId);
        if (criterion) {
          criteriaToGroup.push(criterion);
          criteriaIdsInfo.push(`${type}索引${index}→ID${criterionId}`);
        } else {
          console.warn(`无法找到标准ID ${criterionId}，跳过`);
        }
      } catch (err) {
        console.error(`加载标准ID ${criterionId} 失败:`, err);
      }
    }

    if (criteriaToGroup.length < 2) {
      throw new Error('OR组至少需要2个标准');
    }

    // 生成一个唯一的组ID
    const groupId = crypto.randomUUID();

    // 更新所有标准，设置组ID和操作符
    for (const criterion of criteriaToGroup) {
      if (!criterion.criterion.project_criterion_id) continue;

      await ruleDesignerService.updateProjectCriterion(
        criterion.criterion.project_criterion_id,
        {
          criteria_group_id: groupId,
          group_operator: 'OR'
        }
      );
    }

    console.log(`成功创建"或"关系组: [${criteriaIdsInfo.join(', ')}]`);
  }

  // 创建单个标准
  async function createCriterion(criterionData: any, type: 'inclusion' | 'exclusion'): Promise<ProjectCriterionWithRule> {
    // 准备参数值
    const parameterValues = JSON.stringify(criterionData.parameter_values);

    // 创建请求对象
    const request: CreateProjectCriterionRequest = {
      project_id: projectId,
      rule_definition_id: criterionData.rule_definition_id,
      criterion_type: type,
      parameter_values: parameterValues,
      is_active: criterionData.is_active !== false, // 默认为true
      display_order: criterionData.display_order
    };

    // 调用服务创建标准
    return await ruleDesignerService.createProjectCriterion(request);
  }



  // 切换标准选择状态
  function toggleCriterionSelection(criterion: ProjectCriterionWithRule) {
    const index = selectedCriteria.findIndex(c =>
      c.criterion.project_criterion_id === criterion.criterion.project_criterion_id
    );

    if (index === -1) {
      // 添加到选中列表
      selectedCriteria = [...selectedCriteria, criterion];
    } else {
      // 从选中列表中移除
      selectedCriteria = selectedCriteria.filter((_, i) => i !== index);
    }
  }

  // 检查标准是否被选中
  function isCriterionSelected(criterion: ProjectCriterionWithRule): boolean {
    return selectedCriteria.some(c =>
      c.criterion.project_criterion_id === criterion.criterion.project_criterion_id
    );
  }

  // 显示分组对话框
  function showGroupCriteriaDialog() {
    if (selectedCriteria.length < 2) {
      error = "请至少选择两个标准进行'或'操作";
      return;
    }

    // 检查所选标准是否都属于同一类型（入组或排除）
    const criterionTypes = new Set(selectedCriteria.map(c => c.criterion.criterion_type));
    if (criterionTypes.size > 1) {
      error = "不能同时对入组标准和排除标准进行'或'操作";
      return;
    }

    // 检查所选标准是否已经在某个组中
    const hasGroupedCriteria = selectedCriteria.some(c => c.criterion.criteria_group_id);
    if (hasGroupedCriteria) {
      error = "已经在'或'组中的标准不能再次分组";
      return;
    }

    showGroupDialog = true;
  }

  // 执行分组操作
  async function groupCriteria() {
    if (selectedCriteria.length < 2) {
      error = "请至少选择两个标准进行'或'操作";
      return;
    }

    try {
      // 生成一个唯一的组ID
      const groupId = crypto.randomUUID();

      // 更新所有选中的标准
      for (const criterion of selectedCriteria) {
        if (!criterion.criterion.project_criterion_id) continue;

        await ruleDesignerService.updateProjectCriterion(
          criterion.criterion.project_criterion_id,
          {
            criteria_group_id: groupId,
            group_operator: 'OR'
          }
        );
      }

      // 关闭对话框并重新加载
      showGroupDialog = false;
      selectedCriteria = [];
      await loadProjectCriteria();

    } catch (err: any) {
      error = `创建'或'组失败: ${err.message || '未知错误'}`;
      console.error('创建"或"组失败:', err);
    }
  }

  // 显示解组对话框
  function showUngroupCriteriaDialog() {
    if (selectedCriteria.length === 0) {
      error = "请选择要解除'或'关系的标准";
      return;
    }

    // 检查所选标准是否都在组中
    const notInGroup = selectedCriteria.some(c => !c.criterion.criteria_group_id);
    if (notInGroup) {
      error = "只能解除已在'或'组中的标准";
      return;
    }

    showUngroupDialog = true;
  }

  // 执行解组操作
  async function ungroupCriteria() {
    if (selectedCriteria.length === 0) return;

    try {
      // 更新所有选中的标准
      for (const criterion of selectedCriteria) {
        if (!criterion.criterion.project_criterion_id) continue;

        await ruleDesignerService.updateProjectCriterion(
          criterion.criterion.project_criterion_id,
          {
            criteria_group_id: "",  // 空字符串将被后端视为 NULL
            group_operator: ""      // 空字符串将被后端视为 NULL
          }
        );
      }

      // 关闭对话框并重新加载
      showUngroupDialog = false;
      selectedCriteria = [];
      await loadProjectCriteria();

    } catch (err: any) {
      error = `解除'或'组失败: ${err.message || '未知错误'}`;
      console.error('解除"或"组失败:', err);
    }
  }

  // 检查标准是否在组中
  function isInGroup(criterion: ProjectCriterionWithRule): boolean {
    return !!criterion.criterion.criteria_group_id;
  }

  // 将标准按组分组
  function groupCriteriaByGroup(criteria: ProjectCriterionWithRule[]): Array<{isGroup: boolean, criteria: ProjectCriterionWithRule[]}> {
    const result: Array<{isGroup: boolean, criteria: ProjectCriterionWithRule[]}> = [];
    const groupMap = new Map<string, ProjectCriterionWithRule[]>();

    // 先将标准按组ID分组
    criteria.forEach(criterion => {
      const groupId = criterion.criterion.criteria_group_id;
      if (groupId) {
        if (!groupMap.has(groupId)) {
          groupMap.set(groupId, []);
        }
        groupMap.get(groupId)!.push(criterion);
      }
    });

    // 将组添加到结果中
    groupMap.forEach(groupCriteria => {
      if (groupCriteria.length > 0) {
        result.push({
          isGroup: true,
          criteria: groupCriteria
        });
      }
    });

    // 添加未分组的标准
    const ungroupedCriteria = criteria.filter(c => !isInGroup(c));
    ungroupedCriteria.forEach(criterion => {
      result.push({
        isGroup: false,
        criteria: [criterion]
      });
    });

    return result;
  }

  // 选择组中的所有标准用于解组
  function selectGroupForUngroup(criteria: ProjectCriterionWithRule[]) {
    selectedCriteria = [...criteria];
  }

  // 全选当前标签页中的所有标准
  function selectAllCriteria() {
    const currentCriteria = activeTab === 'inclusion' ? inclusionCriteria : exclusionCriteria;

    // 如果当前已经全选了，则取消全选
    const allSelected = currentCriteria.length > 0 &&
                        currentCriteria.every(c => selectedCriteria.some(
                          sc => sc.criterion.project_criterion_id === c.criterion.project_criterion_id
                        ));

    if (allSelected) {
      // 取消选择当前标签页中的所有标准
      selectedCriteria = selectedCriteria.filter(c =>
        c.criterion.criterion_type !== activeTab
      );
    } else {
      // 先移除当前标签页中已选择的标准，避免重复
      const otherTabSelected = selectedCriteria.filter(c =>
        c.criterion.criterion_type !== activeTab
      );

      // 然后添加当前标签页中的所有标准
      selectedCriteria = [...otherTabSelected, ...currentCriteria];
    }
  }

  // 直接解除组中所有标准的"或"关系
  async function ungroupCriteriaDirectly(criteria: ProjectCriterionWithRule[]) {
    try {
      // 更新所有组内标准
      for (const criterion of criteria) {
        if (!criterion.criterion.project_criterion_id) continue;

        await ruleDesignerService.updateProjectCriterion(
          criterion.criterion.project_criterion_id,
          {
            criteria_group_id: "",  // 空字符串将被后端视为 NULL
            group_operator: ""      // 空字符串将被后端视为 NULL
          }
        );
      }

      // 重新加载数据
      await loadProjectCriteria();

    } catch (err: any) {
      error = `解除'或'组失败: ${err.message || '未知错误'}`;
      console.error('解除"或"组失败:', err);
    }
  }

  // 格式化参数值显示
  function formatParameterValues(criterion: ProjectCriterionWithRule): string {
    try {
      if (!criterion.rule_definition.parameter_schema) {
        return '无参数定义';
      }

      if (!criterion.criterion.parameter_values) {
        return '无参数值';
      }

      const schema = ruleDesignerService.parseParameterSchema(criterion.rule_definition.parameter_schema);
      const values = ruleDesignerService.parseParameterValues(criterion.criterion.parameter_values);

      if (!schema.parameters || !Array.isArray(schema.parameters) || schema.parameters.length === 0) {
        return '无参数定义';
      }

      const formattedValues = schema.parameters.map(param => {
        const value = values[param.name];

        if (value === undefined || value === null) return null;

        let displayValue = value;
        if (param.unit) {
          displayValue = `${value} ${param.unit}`;
        }

        return `${param.label}: ${displayValue}`;
      }).filter(Boolean);

      return formattedValues.join(', ');
    } catch (e) {
      console.error('格式化参数值失败:', e);
      return '参数格式错误';
    }
  }

  // 组件挂载时初始化
  onMount(() => {
    loadProjectCriteria();
  });
</script>

<div class="w-full">


  {#if error}
    <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
      <span class="block sm:inline">{error}</span>
    </div>
  {/if}

  <div class="flex justify-between items-center mb-6">
    <h2 class="text-xl font-semibold text-slate-900 dark:text-slate-100">项目入组/排除标准</h2>
    <div class="flex gap-2">
      <Button
        onclick={() => showGroupCriteriaDialog()}
        variant="outline"
        class="flex items-center gap-1 shadow-sm hover:shadow transition-shadow"
        disabled={selectedCriteria.length < 2}
      >
        <span>设置"或"关系</span>
      </Button>
      <Button
        onclick={() => showUngroupCriteriaDialog()}
        variant="outline"
        class="flex items-center gap-1 shadow-sm hover:shadow transition-shadow"
        disabled={selectedCriteria.length === 0 || !selectedCriteria.some(c => c.criterion.criteria_group_id)}
      >
        <span>解除"或"关系</span>
      </Button>
      <Button
        onclick={() => showBatchDeleteConfirmation()}
        variant="outline"
        class="flex items-center gap-1 shadow-sm hover:shadow transition-shadow text-red-600 hover:text-red-700 hover:bg-red-50 dark:text-red-400 dark:hover:text-red-300 dark:hover:bg-red-900/20"
        disabled={selectedCriteria.length === 0}
      >
        <Trash2 class="h-4 w-4" />
        <span>批量删除</span>
      </Button>
      <Button
        onclick={() => showImportJsonDialog()}
        variant="outline"
        class="flex items-center gap-1 shadow-sm hover:shadow transition-shadow"
      >
        <Upload class="h-4 w-4" />
        <span>导入JSON</span>
      </Button>
      <Button onclick={addCriterion} variant="default" class="flex items-center gap-1 shadow-sm hover:shadow transition-shadow">
        <Plus class="h-4 w-4" />
        <span>添加标准</span>
      </Button>
    </div>
  </div>

  {#if isLoading}
    <div class="flex justify-center items-center py-12">
      <div class="inline-block w-6 h-6 border-2 border-t-transparent border-blue-600 rounded-full animate-spin"></div>
      <span class="ml-2 text-slate-600 dark:text-slate-300">加载中...</span>
    </div>
  {:else}
    <!-- 标签页实现 -->
    <div class="w-full">
      <!-- 标签页头部 -->
      <div class="flex justify-between border-b mb-4">
        <div class="flex">
          <button
            class="px-4 py-2 border-b-2 font-medium text-center transition-colors {activeTab === 'inclusion' ? 'border-blue-500 text-blue-600 dark:border-blue-400 dark:text-blue-300' : 'border-transparent hover:text-blue-600 hover:border-blue-400 dark:hover:text-blue-300 dark:hover:border-blue-400'}"
            onclick={() => activeTab = 'inclusion'}
          >
            入组标准 ({inclusionCriteria.length})
          </button>
          <button
            class="px-4 py-2 border-b-2 font-medium text-center transition-colors {activeTab === 'exclusion' ? 'border-blue-500 text-blue-600 dark:border-blue-400 dark:text-blue-300' : 'border-transparent hover:text-blue-600 hover:border-blue-400 dark:hover:text-blue-300 dark:hover:border-blue-400'}"
            onclick={() => activeTab = 'exclusion'}
          >
            排除标准 ({exclusionCriteria.length})
          </button>
        </div>

        <!-- 全选按钮 -->
        {#if (activeTab === 'inclusion' && inclusionCriteria.length > 0) || (activeTab === 'exclusion' && exclusionCriteria.length > 0)}
          <Button
            onclick={selectAllCriteria}
            variant="ghost"
            size="sm"
            class="flex items-center gap-1 mr-2 mb-1"
          >
            <CheckSquare class="h-4 w-4" />
            <span>
              {#if activeTab === 'inclusion'}
                {inclusionCriteria.every(c => selectedCriteria.some(sc => sc.criterion.project_criterion_id === c.criterion.project_criterion_id)) ? '取消全选' : '全选'}
              {:else}
                {exclusionCriteria.every(c => selectedCriteria.some(sc => sc.criterion.project_criterion_id === c.criterion.project_criterion_id)) ? '取消全选' : '全选'}
              {/if}
            </span>
          </Button>
        {/if}
      </div>

      <!-- 入组标准内容 -->
      {#if activeTab === 'inclusion'}
        <div class="mt-4">
          {#if inclusionCriteria.length === 0}
            <div class="bg-slate-50 dark:bg-slate-800 rounded-md p-8 text-center">
              <p class="text-slate-500 dark:text-slate-400">暂无入组标准，点击"添加标准"按钮添加</p>
            </div>
          {:else}
            <div class="space-y-4">
              {#each groupCriteriaByGroup(inclusionCriteria) as group}
                {#if group.isGroup}
                  <!-- 或关系组 -->
                  <div class="relative mb-4">
                    <!-- 组外框 -->
                    <div class="w-full text-left border-2 border-blue-500 rounded-md p-1 bg-blue-50/50 dark:bg-blue-900/10 relative group">
                      <!-- 选择组按钮 -->
                      <button
                        type="button"
                        class="absolute inset-0 w-full h-full opacity-0 cursor-pointer z-10"
                        onclick={() => selectGroupForUngroup(group.criteria)}
                        ondblclick={() => ungroupCriteriaDirectly(group.criteria)}
                        aria-label="选择或关系组（双击解除'或'关系）"
                        title="双击解除'或'关系"
                      ></button>
                      <!-- 组标题 -->
                      <div class="absolute -top-3 left-3 bg-white dark:bg-slate-900 px-2 text-blue-600 dark:text-blue-400 font-medium text-sm">
                        或关系组 <span class="text-xs text-slate-500">(双击解除)</span>
                      </div>

                      <!-- 解组按钮 -->
                      <div class="absolute -top-3 right-3">
                        <button
                          type="button"
                          class="bg-white dark:bg-slate-900 p-1 rounded-full text-blue-600 hover:text-blue-800 hover:bg-blue-50 dark:text-blue-400 dark:hover:text-blue-300 dark:hover:bg-blue-900/30 transition-colors opacity-0 group-hover:opacity-100"
                          onclick={(e) => {
                            e.stopPropagation();
                            selectGroupForUngroup(group.criteria);
                            showUngroupCriteriaDialog();
                          }}
                          title="解除'或'关系"
                        >
                          <span class="text-xs px-2 py-0.5 border border-blue-500 rounded-full">解除</span>
                        </button>
                      </div>

                      <!-- 组内标准 -->
                      <div class="space-y-2 mt-2">
                        {#each group.criteria as criterion}
                          <div class={`
                            bg-white dark:bg-slate-900 border border-slate-200 dark:border-slate-700 rounded-md p-4 shadow-sm hover:shadow-md transition-shadow
                            ${isCriterionSelected(criterion) ? 'bg-blue-50 dark:bg-blue-900/20' : ''}
                          `}>
                            <div class="flex justify-between items-start">
                              <div class="flex items-start gap-3">
                                <input
                                  type="checkbox"
                                  class="mt-1.5 h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                                  checked={isCriterionSelected(criterion)}
                                  onclick={(e) => {
                                    e.stopPropagation();
                                    toggleCriterionSelection(criterion);
                                  }}
                                />
                                <div>
                                  <div class="flex items-center gap-2">
                                    <h3 class="text-lg font-medium text-slate-900 dark:text-slate-100">{criterion.rule_definition.rule_name}</h3>
                                    <span class="inline-block px-2 py-0.5 text-xs rounded-full bg-blue-100 text-blue-800 dark:bg-blue-900/40 dark:text-blue-300">
                                      或
                                    </span>
                                  </div>
                                  <div class="mt-2 ml-1">
                                    <span class="inline-block px-3 py-1.5 bg-blue-100 text-blue-800 border border-blue-200 rounded-md text-sm font-medium shadow-sm">
                                      {formatParameterValues(criterion)}
                                    </span>
                                  </div>
                                </div>
                              </div>
                              <div class="flex space-x-2">
                                <button
                                  type="button"
                                  class="p-1 rounded-full text-blue-600 hover:text-blue-800 hover:bg-blue-50 dark:text-blue-400 dark:hover:text-blue-300 dark:hover:bg-blue-900/30 transition-colors"
                                  onclick={(e) => {
                                    e.stopPropagation();
                                    editCriterion(criterion);
                                  }}
                                  title="编辑"
                                >
                                  <Edit class="h-4 w-4" />
                                </button>
                                <button
                                  type="button"
                                  class="p-1 rounded-full text-red-600 hover:text-red-800 hover:bg-red-50 dark:text-red-400 dark:hover:text-red-300 dark:hover:bg-red-900/30 transition-colors"
                                  onclick={(e) => {
                                    e.stopPropagation();
                                    confirmDeleteCriterion(criterion);
                                  }}
                                  title="删除"
                                >
                                  <Trash2 class="h-4 w-4" />
                                </button>
                              </div>
                            </div>
                          </div>
                        {/each}
                      </div>
                    </div>
                  </div>
                {:else}
                  <!-- 单个标准 -->
                  {#each group.criteria as criterion}
                    <div class={`
                      bg-white dark:bg-slate-900 border border-slate-200 dark:border-slate-700 rounded-md p-4 shadow-sm hover:shadow-md transition-shadow mb-4
                      ${isCriterionSelected(criterion) ? 'bg-blue-50 dark:bg-blue-900/20' : ''}
                    `}>
                      <div class="flex justify-between items-start">
                        <div class="flex items-start gap-3">
                          <input
                            type="checkbox"
                            class="mt-1.5 h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                            checked={isCriterionSelected(criterion)}
                            onclick={() => toggleCriterionSelection(criterion)}
                          />
                          <div>
                            <h3 class="text-lg font-medium text-slate-900 dark:text-slate-100">{criterion.rule_definition.rule_name}</h3>
                            <div class="mt-2 ml-1">
                              <span class="inline-block px-3 py-1.5 bg-blue-100 text-blue-800 border border-blue-200 rounded-md text-sm font-medium shadow-sm">
                                {formatParameterValues(criterion)}
                              </span>
                            </div>
                          </div>
                        </div>
                        <div class="flex space-x-2">
                          <button
                            type="button"
                            class="p-1 rounded-full text-slate-500 hover:text-slate-700 hover:bg-slate-100 dark:text-slate-400 dark:hover:text-slate-300 dark:hover:bg-slate-800 transition-colors"
                            onclick={() => moveCriterion(criterion, 'up')}
                            title="上移"
                          >
                            <ArrowUp class="h-4 w-4" />
                          </button>
                          <button
                            type="button"
                            class="p-1 rounded-full text-slate-500 hover:text-slate-700 hover:bg-slate-100 dark:text-slate-400 dark:hover:text-slate-300 dark:hover:bg-slate-800 transition-colors"
                            onclick={() => moveCriterion(criterion, 'down')}
                            title="下移"
                          >
                            <ArrowDown class="h-4 w-4" />
                          </button>
                          <button
                            type="button"
                            class="p-1 rounded-full text-blue-600 hover:text-blue-800 hover:bg-blue-50 dark:text-blue-400 dark:hover:text-blue-300 dark:hover:bg-blue-900/30 transition-colors"
                            onclick={() => editCriterion(criterion)}
                            title="编辑"
                          >
                            <Edit class="h-4 w-4" />
                          </button>
                          <button
                            type="button"
                            class="p-1 rounded-full text-red-600 hover:text-red-800 hover:bg-red-50 dark:text-red-400 dark:hover:text-red-300 dark:hover:bg-red-900/30 transition-colors"
                            onclick={() => confirmDeleteCriterion(criterion)}
                            title="删除"
                          >
                            <Trash2 class="h-4 w-4" />
                          </button>
                        </div>
                      </div>
                    </div>
                  {/each}
                {/if}
              {/each}
            </div>
          {/if}
        </div>
      {/if}

      <!-- 排除标准内容 -->
      {#if activeTab === 'exclusion'}
        <div class="mt-4">
          {#if exclusionCriteria.length === 0}
            <div class="bg-slate-50 dark:bg-slate-800 rounded-md p-8 text-center">
              <p class="text-slate-500 dark:text-slate-400">暂无排除标准，点击"添加标准"按钮添加</p>
            </div>
          {:else}
            <div class="space-y-4">
              {#each groupCriteriaByGroup(exclusionCriteria) as group}
                {#if group.isGroup}
                  <!-- 或关系组 -->
                  <div class="relative mb-4">
                    <!-- 组外框 -->
                    <div class="w-full text-left border-2 border-blue-500 rounded-md p-1 bg-blue-50/50 dark:bg-blue-900/10 relative group">
                      <!-- 选择组按钮 -->
                      <button
                        type="button"
                        class="absolute inset-0 w-full h-full opacity-0 cursor-pointer z-10"
                        onclick={() => selectGroupForUngroup(group.criteria)}
                        ondblclick={() => ungroupCriteriaDirectly(group.criteria)}
                        aria-label="选择或关系组（双击解除'或'关系）"
                        title="双击解除'或'关系"
                      ></button>
                      <!-- 组标题 -->
                      <div class="absolute -top-3 left-3 bg-white dark:bg-slate-900 px-2 text-blue-600 dark:text-blue-400 font-medium text-sm">
                        或关系组 <span class="text-xs text-slate-500">(双击解除)</span>
                      </div>

                      <!-- 解组按钮 -->
                      <div class="absolute -top-3 right-3">
                        <button
                          type="button"
                          class="bg-white dark:bg-slate-900 p-1 rounded-full text-blue-600 hover:text-blue-800 hover:bg-blue-50 dark:text-blue-400 dark:hover:text-blue-300 dark:hover:bg-blue-900/30 transition-colors opacity-0 group-hover:opacity-100"
                          onclick={(e) => {
                            e.stopPropagation();
                            selectGroupForUngroup(group.criteria);
                            showUngroupCriteriaDialog();
                          }}
                          title="解除'或'关系"
                        >
                          <span class="text-xs px-2 py-0.5 border border-blue-500 rounded-full">解除</span>
                        </button>
                      </div>

                      <!-- 组内标准 -->
                      <div class="space-y-2 mt-2">
                        {#each group.criteria as criterion}
                          <div class={`
                            bg-white dark:bg-slate-900 border border-slate-200 dark:border-slate-700 rounded-md p-4 shadow-sm hover:shadow-md transition-shadow
                            ${isCriterionSelected(criterion) ? 'bg-blue-50 dark:bg-blue-900/20' : ''}
                          `}>
                            <div class="flex justify-between items-start">
                              <div class="flex items-start gap-3">
                                <input
                                  type="checkbox"
                                  class="mt-1.5 h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                                  checked={isCriterionSelected(criterion)}
                                  onclick={(e) => {
                                    e.stopPropagation();
                                    toggleCriterionSelection(criterion);
                                  }}
                                />
                                <div>
                                  <div class="flex items-center gap-2">
                                    <h3 class="text-lg font-medium text-slate-900 dark:text-slate-100">{criterion.rule_definition.rule_name}</h3>
                                    <span class="inline-block px-2 py-0.5 text-xs rounded-full bg-blue-100 text-blue-800 dark:bg-blue-900/40 dark:text-blue-300">
                                      或
                                    </span>
                                  </div>
                                  <div class="mt-2 ml-1">
                                    <span class="inline-block px-3 py-1.5 bg-blue-100 text-blue-800 border border-blue-200 rounded-md text-sm font-medium shadow-sm">
                                      {formatParameterValues(criterion)}
                                    </span>
                                  </div>
                                </div>
                              </div>
                              <div class="flex space-x-2">
                                <button
                                  type="button"
                                  class="p-1 rounded-full text-blue-600 hover:text-blue-800 hover:bg-blue-50 dark:text-blue-400 dark:hover:text-blue-300 dark:hover:bg-blue-900/30 transition-colors"
                                  onclick={(e) => {
                                    e.stopPropagation();
                                    editCriterion(criterion);
                                  }}
                                  title="编辑"
                                >
                                  <Edit class="h-4 w-4" />
                                </button>
                                <button
                                  type="button"
                                  class="p-1 rounded-full text-red-600 hover:text-red-800 hover:bg-red-50 dark:text-red-400 dark:hover:text-red-300 dark:hover:bg-red-900/30 transition-colors"
                                  onclick={(e) => {
                                    e.stopPropagation();
                                    confirmDeleteCriterion(criterion);
                                  }}
                                  title="删除"
                                >
                                  <Trash2 class="h-4 w-4" />
                                </button>
                              </div>
                            </div>
                          </div>
                        {/each}
                      </div>
                    </div>
                  </div>
                {:else}
                  <!-- 单个标准 -->
                  {#each group.criteria as criterion}
                    <div class={`
                      bg-white dark:bg-slate-900 border border-slate-200 dark:border-slate-700 rounded-md p-4 shadow-sm hover:shadow-md transition-shadow mb-4
                      ${isCriterionSelected(criterion) ? 'bg-blue-50 dark:bg-blue-900/20' : ''}
                    `}>
                      <div class="flex justify-between items-start">
                        <div class="flex items-start gap-3">
                          <input
                            type="checkbox"
                            class="mt-1.5 h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                            checked={isCriterionSelected(criterion)}
                            onclick={() => toggleCriterionSelection(criterion)}
                          />
                          <div>
                            <h3 class="text-lg font-medium text-slate-900 dark:text-slate-100">{criterion.rule_definition.rule_name}</h3>
                            <div class="mt-2 ml-1">
                              <span class="inline-block px-3 py-1.5 bg-blue-100 text-blue-800 border border-blue-200 rounded-md text-sm font-medium shadow-sm">
                                {formatParameterValues(criterion)}
                              </span>
                            </div>
                          </div>
                        </div>
                        <div class="flex space-x-2">
                          <button
                            type="button"
                            class="p-1 rounded-full text-slate-500 hover:text-slate-700 hover:bg-slate-100 dark:text-slate-400 dark:hover:text-slate-300 dark:hover:bg-slate-800 transition-colors"
                            onclick={() => moveCriterion(criterion, 'up')}
                            title="上移"
                          >
                            <ArrowUp class="h-4 w-4" />
                          </button>
                          <button
                            type="button"
                            class="p-1 rounded-full text-slate-500 hover:text-slate-700 hover:bg-slate-100 dark:text-slate-400 dark:hover:text-slate-300 dark:hover:bg-slate-800 transition-colors"
                            onclick={() => moveCriterion(criterion, 'down')}
                            title="下移"
                          >
                            <ArrowDown class="h-4 w-4" />
                          </button>
                          <button
                            type="button"
                            class="p-1 rounded-full text-blue-600 hover:text-blue-800 hover:bg-blue-50 dark:text-blue-400 dark:hover:text-blue-300 dark:hover:bg-blue-900/30 transition-colors"
                            onclick={() => editCriterion(criterion)}
                            title="编辑"
                          >
                            <Edit class="h-4 w-4" />
                          </button>
                          <button
                            type="button"
                            class="p-1 rounded-full text-red-600 hover:text-red-800 hover:bg-red-50 dark:text-red-400 dark:hover:text-red-300 dark:hover:bg-red-900/30 transition-colors"
                            onclick={() => confirmDeleteCriterion(criterion)}
                            title="删除"
                          >
                            <Trash2 class="h-4 w-4" />
                          </button>
                        </div>
                      </div>
                    </div>
                  {/each}
                {/if}
              {/each}
            </div>
          {/if}
        </div>
      {/if}
    </div>
  {/if}

  <!-- 添加标准对话框 -->
  <Dialog.Root
    bind:open={showAddDialog}
    onOpenChange={(open) => {
      if (!open) handleDialogClose();
    }}
  >
    <Dialog.Content class="sm:max-w-[800px] focus:outline-none" data-dialog="add-criterion">
      <Dialog.Header>
        <Dialog.Title>添加{activeTab === 'inclusion' ? '入组' : '排除'}标准</Dialog.Title>
        <Dialog.Description>
          为项目添加一个新的{activeTab === 'inclusion' ? '入组' : '排除'}标准。
        </Dialog.Description>
      </Dialog.Header>

      <div class="py-4" style="pointer-events: auto;">
        <ProjectCriterionForm
          projectId={projectId}
          criterionType={activeTab as 'inclusion' | 'exclusion'}
          onSuccess={() => {
            showAddDialog = false;
            loadProjectCriteria();
          }}
        />
      </div>

      <Dialog.Footer>
        <Button variant="outline" onclick={() => showAddDialog = false} class="min-w-[80px]">
          关闭
        </Button>
      </Dialog.Footer>
    </Dialog.Content>
  </Dialog.Root>

  <!-- 编辑标准对话框 -->
  <Dialog.Root
    bind:open={showEditDialog}
    onOpenChange={(open) => {
      if (!open) handleDialogClose();
    }}
  >
    <Dialog.Content class="sm:max-w-[800px]">
      <Dialog.Header>
        <Dialog.Title>编辑{selectedCriterion?.criterion.criterion_type === 'inclusion' ? '入组' : '排除'}标准</Dialog.Title>
        <Dialog.Description>
          编辑项目的{selectedCriterion?.criterion.criterion_type === 'inclusion' ? '入组' : '排除'}标准。
        </Dialog.Description>
      </Dialog.Header>

      <div class="py-4">
        {#if selectedCriterion}
          <ProjectCriterionForm
            projectId={projectId}
            criterionType={selectedCriterion.criterion.criterion_type as 'inclusion' | 'exclusion'}
            projectCriterion={selectedCriterion}
            onSuccess={() => {
              showEditDialog = false;
              loadProjectCriteria();
            }}
          />
        {/if}
      </div>

      <Dialog.Footer>
        <Button variant="outline" onclick={() => showEditDialog = false} class="min-w-[80px]">
          关闭
        </Button>
      </Dialog.Footer>
    </Dialog.Content>
  </Dialog.Root>

  <!-- 删除确认对话框 -->
  <Dialog.Root
    bind:open={showDeleteDialog}
    onOpenChange={(open) => {
      if (!open) handleDialogClose();
    }}
  >
    <Dialog.Content>
      <Dialog.Header>
        <Dialog.Title>删除{selectedCriterion?.criterion.criterion_type === 'inclusion' ? '入组' : '排除'}标准</Dialog.Title>
        <Dialog.Description>
          确定要删除此{selectedCriterion?.criterion.criterion_type === 'inclusion' ? '入组' : '排除'}标准吗？此操作不可撤销。
        </Dialog.Description>
      </Dialog.Header>

      <div class="py-4">
        {#if selectedCriterion}
          <p class="text-sm text-slate-500 dark:text-slate-400">
            将删除规则 "{selectedCriterion.rule_definition.rule_name}" 的标准。
          </p>
        {/if}
      </div>

      <Dialog.Footer>
        <Button variant="outline" onclick={() => showDeleteDialog = false} class="min-w-[80px]">
          取消
        </Button>
        <Button variant="destructive" onclick={deleteCriterion} class="min-w-[80px]">
          删除
        </Button>
      </Dialog.Footer>
    </Dialog.Content>
  </Dialog.Root>

  <!-- 分组确认对话框 -->
  <Dialog.Root
    bind:open={showGroupDialog}
    onOpenChange={(open) => {
      if (!open) handleDialogClose();
    }}
  >
    <Dialog.Content>
      <Dialog.Header>
        <Dialog.Title>设置"或"关系</Dialog.Title>
        <Dialog.Description>
          将选中的标准设置为"或"关系，表示满足其中任意一个即可。
        </Dialog.Description>
      </Dialog.Header>

      <div class="py-4">
        <p class="text-sm text-slate-500 dark:text-slate-400 mb-4">
          已选择 {selectedCriteria.length} 个标准：
        </p>
        <div class="space-y-2 max-h-60 overflow-y-auto p-2 border rounded-md">
          {#each selectedCriteria as criterion}
            <div class="p-2 bg-slate-50 dark:bg-slate-800 rounded">
              <p class="font-medium">{criterion.rule_definition.rule_name}</p>
              <div class="mt-1">
                <span class="inline-block px-2 py-1 bg-blue-50 text-blue-700 border border-blue-100 rounded text-xs font-medium">
                  {formatParameterValues(criterion)}
                </span>
              </div>
            </div>
          {/each}
        </div>
      </div>

      <Dialog.Footer>
        <Button variant="outline" onclick={() => showGroupDialog = false} class="min-w-[80px]">
          取消
        </Button>
        <Button variant="default" onclick={groupCriteria} class="min-w-[80px]">
          确认设置
        </Button>
      </Dialog.Footer>
    </Dialog.Content>
  </Dialog.Root>

  <!-- 解组确认对话框 -->
  <Dialog.Root
    bind:open={showUngroupDialog}
    onOpenChange={(open) => {
      if (!open) handleDialogClose();
    }}
  >
    <Dialog.Content>
      <Dialog.Header>
        <Dialog.Title>解除"或"关系</Dialog.Title>
        <Dialog.Description>
          将选中的标准从"或"组中移除，恢复为独立标准。
        </Dialog.Description>
      </Dialog.Header>

      <div class="py-4">
        <p class="text-sm text-slate-500 dark:text-slate-400 mb-4">
          已选择 {selectedCriteria.length} 个标准：
        </p>
        <div class="space-y-2 max-h-60 overflow-y-auto p-2 border rounded-md">
          {#each selectedCriteria as criterion}
            <div class="p-2 bg-slate-50 dark:bg-slate-800 rounded">
              <p class="font-medium">{criterion.rule_definition.rule_name}</p>
              <div class="mt-1">
                <span class="inline-block px-2 py-1 bg-blue-50 text-blue-700 border border-blue-100 rounded text-xs font-medium">
                  {formatParameterValues(criterion)}
                </span>
              </div>
            </div>
          {/each}
        </div>
      </div>

      <Dialog.Footer>
        <Button variant="outline" onclick={() => showUngroupDialog = false} class="min-w-[80px]">
          取消
        </Button>
        <Button variant="default" onclick={ungroupCriteria} class="min-w-[80px]">
          确认解除
        </Button>
      </Dialog.Footer>
    </Dialog.Content>
  </Dialog.Root>

  <!-- 批量删除确认对话框 -->
  <Dialog.Root
    bind:open={showBatchDeleteDialog}
    onOpenChange={(open) => {
      if (!open) handleDialogClose();
    }}
  >
    <Dialog.Content>
      <Dialog.Header>
        <Dialog.Title>批量删除标准</Dialog.Title>
        <Dialog.Description>
          确定要删除选中的 {selectedCriteria.length} 个标准吗？此操作不可撤销。
        </Dialog.Description>
      </Dialog.Header>

      <div class="py-4">
        <div class="bg-red-50 dark:bg-red-900/20 p-3 rounded-md border border-red-200 dark:border-red-800 mb-4">
          <p class="text-sm text-red-600 dark:text-red-400 flex items-center">
            <AlertCircle class="h-4 w-4 mr-1 flex-shrink-0" />
            <span>此操作将永久删除选中的标准，无法恢复。</span>
          </p>
        </div>

        <div class="flex justify-between items-center mb-2">
          <p class="text-sm text-slate-500 dark:text-slate-400">
            已选择 {selectedCriteria.length} 个标准：
          </p>
          <Button
            onclick={toggleAllInBatchDeleteDialog}
            variant="ghost"
            size="sm"
            class="flex items-center gap-1"
          >
            <CheckSquare class="h-4 w-4" />
            <span>
              {#if activeTab === 'inclusion'}
                {inclusionCriteria.every(c => selectedCriteria.some(sc => sc.criterion.project_criterion_id === c.criterion.project_criterion_id)) ? '取消全选' : '全选'}
              {:else}
                {exclusionCriteria.every(c => selectedCriteria.some(sc => sc.criterion.project_criterion_id === c.criterion.project_criterion_id)) ? '取消全选' : '全选'}
              {/if}
            </span>
          </Button>
        </div>
        <div class="space-y-2 max-h-60 overflow-y-auto p-2 border rounded-md">
          {#each selectedCriteria as criterion}
            <div class="p-2 bg-slate-50 dark:bg-slate-800 rounded flex justify-between items-center">
              <div>
                <p class="font-medium">{criterion.rule_definition.rule_name}</p>
                <div class="mt-1">
                  <span class="inline-block px-2 py-1 bg-blue-50 text-blue-700 border border-blue-100 rounded text-xs font-medium">
                    {formatParameterValues(criterion)}
                  </span>
                </div>
              </div>
              <div class="text-xs text-slate-500 dark:text-slate-400">
                {criterion.criterion.criterion_type === 'inclusion' ? '入组标准' : '排除标准'}
              </div>
            </div>
          {/each}
        </div>
      </div>

      <Dialog.Footer>
        <Button variant="outline" onclick={() => showBatchDeleteDialog = false} class="min-w-[80px]">
          取消
        </Button>
        <Button variant="destructive" onclick={batchDeleteCriteria} class="min-w-[80px]">
          确认删除
        </Button>
      </Dialog.Footer>
    </Dialog.Content>
  </Dialog.Root>

  <!-- 导入JSON对话框 -->
  <Dialog.Root
    bind:open={showImportDialog}
    onOpenChange={(open) => {
      if (!open) handleDialogClose();
    }}
  >
    <Dialog.Content>
      <Dialog.Header>
        <Dialog.Title>导入JSON</Dialog.Title>
        <Dialog.Description>
          通过JSON文件批量导入入组/排除标准。
        </Dialog.Description>
      </Dialog.Header>

      <div class="py-4">
        {#if importError}
          <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
            <span class="block sm:inline">{importError}</span>
          </div>
        {/if}

        <div class="space-y-4">
          <div class="bg-blue-50 dark:bg-blue-900/20 p-3 rounded-md border border-blue-200 dark:border-blue-800">
            <div class="flex justify-between items-center">
              <div class="flex items-center">
                <AlertCircle class="h-4 w-4 mr-1 text-blue-800 dark:text-blue-300" />
                <h3 class="text-sm font-medium text-blue-800 dark:text-blue-300">JSON格式说明</h3>
              </div>
              <button
                type="button"
                onclick={copyExampleToClipboard}
                class="bg-blue-100 dark:bg-blue-800 py-1 px-2 rounded text-xs text-blue-700 dark:text-blue-300 hover:bg-blue-200 dark:hover:bg-blue-700 transition-colors font-medium"
                title="复制到剪贴板并填充到下方文本框"
              >
                复制并填充到文本框
              </button>
            </div>

            <div class="flex justify-between items-center mt-2 mb-1">
              <p class="text-xs text-slate-600 dark:text-slate-400">下载模板：</p>
              <div class="flex gap-1">
                <Button
                  variant="outline"
                  size="sm"
                  onclick={() => downloadTemplateFile('simple')}
                  class="text-xs py-0.5 px-2 h-auto flex items-center gap-1"
                >
                  <Download class="h-3 w-3" />
                  简单模板
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onclick={() => downloadTemplateFile('detailed')}
                  class="text-xs py-0.5 px-2 h-auto flex items-center gap-1"
                >
                  <Download class="h-3 w-3" />
                  详细模板
                </Button>
              </div>
            </div>

            <div class="relative mt-2">
              <div class="bg-white dark:bg-slate-800 p-2 rounded text-xs border border-slate-200 dark:border-slate-700 max-h-32 overflow-y-auto">
                <code class="text-xs whitespace-pre-wrap">
                  <span class="text-blue-600 dark:text-blue-400">// 示例JSON结构 - 推荐格式</span><br/>
                  &#123;<br/>
                  &nbsp;&nbsp;"criteria": [<br/>
                  &nbsp;&nbsp;&nbsp;&nbsp;&#123; "rule_definition_id": 4, "parameter_values": &#123; "Min_age": 40, "Max_age": 80 &#125;, "display_order": 1 &#125;,<br/>
                  &nbsp;&nbsp;&nbsp;&nbsp;&#123; "rule_definition_id": 7, "parameter_values": &#123; "diagnose_month": 12 &#125;, "display_order": 2 &#125;,<br/>
                  &nbsp;&nbsp;&nbsp;&nbsp;&#123; "rule_definition_id": 17, "parameter_values": &#123; "date_number": 12, "Min_count": 2, "degree": "中度" &#125;, "display_order": 6 &#125;,<br/>
                  &nbsp;&nbsp;&nbsp;&nbsp;&#123; "rule_definition_id": 17, "parameter_values": &#123; "date_number": 12, "Min_count": 1, "degree": "重度" &#125;, "display_order": 7 &#125;<br/>
                  &nbsp;&nbsp;],<br/>
                  &nbsp;&nbsp;"or_groups": [<br/>
                  &nbsp;&nbsp;&nbsp;&nbsp;&#123; "group_id": 1, "criteria_ids": [6, 7], "operator": "OR" &#125;<br/>
                  &nbsp;&nbsp;]<br/>
                  &#125;<br/>
                  <br/>
                  <span class="text-blue-600 dark:text-blue-400">// 系统会根据rule_definition_id自动查询规则的category字段</span><br/>
                  <span class="text-blue-600 dark:text-blue-400">// 判断是入组还是排除标准</span><br/>
                  <span class="text-blue-600 dark:text-blue-400">// 重要：or_groups中的criteria_ids对应criteria数组中的索引（从1开始）</span><br/>
                  <span class="text-blue-600 dark:text-blue-400">// 例如criteria_ids: [6, 7]表示criteria数组中第6个和第7个项目</span>
                </code>
              </div>
            </div>
          </div>

          <div class="flex gap-4 mt-3">
            <!-- 左侧：文件上传 -->
            <div class="border-2 border-dashed border-slate-300 dark:border-slate-700 rounded-lg p-3 text-center flex-1">
              {#if importInProgress}
                <div class="flex flex-col items-center justify-center py-2">
                  <div class="inline-block w-6 h-6 border-2 border-t-transparent border-blue-600 rounded-full animate-spin mb-1"></div>
                  <span class="text-xs font-medium text-slate-700 dark:text-slate-300">正在处理...</span>
                </div>
              {:else}
                <input
                  type="file"
                  id="json-file-input"
                  accept=".json,application/json"
                  onchange={handleJsonImport}
                  class="hidden"
                />
                <label
                  for="json-file-input"
                  class="cursor-pointer flex flex-col items-center justify-center h-full"
                >
                  <Upload class="h-6 w-6 text-blue-500 dark:text-blue-400 mb-1" />
                  <span class="text-xs font-medium text-slate-700 dark:text-slate-300">选择JSON文件</span>
                  <span class="text-xs text-slate-500 dark:text-slate-400">或拖放文件</span>
                </label>
              {/if}
            </div>

            <!-- 右侧：文本粘贴 -->
            <div class="flex flex-col flex-1">
              <div class="flex justify-between items-center mb-1">
                <h3 class="text-xs font-medium text-slate-700 dark:text-slate-300">直接粘贴JSON代码</h3>
                <Button
                  variant="default"
                  size="sm"
                  onclick={handlePastedJsonImport}
                  disabled={importInProgress}
                  class="text-xs py-0.5 px-2 h-auto"
                >
                  处理JSON
                </Button>
              </div>
              <textarea
                id="json-paste-area"
                bind:value={jsonPasteContent}
                placeholder="在此粘贴JSON代码..."
                class="w-full h-24 p-2 border border-slate-300 dark:border-slate-600 rounded-md text-xs text-slate-900 dark:text-slate-100 bg-white dark:bg-slate-800 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-600 resize-none"
                disabled={importInProgress}
                data-custom-paste="true"
              ></textarea>
            </div>
          </div>
        </div>
      </div>

      <Dialog.Footer class="flex justify-between border-t pt-3">
        <div class="text-xs text-slate-500 dark:text-slate-400">
          提示：点击"复制并填充到文本框"可快速获取示例
        </div>
        <Button variant="outline" onclick={() => showImportDialog = false} class="min-w-[80px]" disabled={importInProgress}>
          {importInProgress ? '处理中...' : '关闭'}
        </Button>
      </Dialog.Footer>
    </Dialog.Content>
  </Dialog.Root>

  <!-- 导入结果对话框 -->
  <Dialog.Root
    bind:open={showImportResultDialog}
    onOpenChange={(open) => {
      if (!open) handleDialogClose();
    }}
  >
    <Dialog.Content class="sm:max-w-[500px]">
      <Dialog.Header class="pb-2">
        <Dialog.Title>导入结果</Dialog.Title>
        <Dialog.Description>
          JSON导入处理完成
        </Dialog.Description>
      </Dialog.Header>

      <div class="py-2">
        <div class="grid grid-cols-3 gap-2 mb-3">
          <div class="text-center p-2 bg-slate-50 dark:bg-slate-800 rounded-md">
            <div class="text-base font-semibold text-slate-700 dark:text-slate-300">{importResults.total}</div>
            <div class="text-xs text-slate-500 dark:text-slate-400">总计</div>
          </div>
          <div class="text-center p-2 bg-green-50 dark:bg-green-900/20 rounded-md">
            <div class="text-base font-semibold text-green-600 dark:text-green-400">{importResults.success}</div>
            <div class="text-xs text-slate-500 dark:text-slate-400">成功</div>
          </div>
          <div class="text-center p-2 bg-red-50 dark:bg-red-900/20 rounded-md">
            <div class="text-base font-semibold text-red-600 dark:text-red-400">{importResults.failed}</div>
            <div class="text-xs text-slate-500 dark:text-slate-400">失败</div>
          </div>
        </div>

        <div>
          <div class="flex justify-between items-center mb-1">
            <h3 class="text-xs font-medium text-slate-700 dark:text-slate-300">详细信息</h3>
            <span class="text-xs text-slate-500 dark:text-slate-400">{importResults.details.length}条记录</span>
          </div>
          <div class="max-h-40 overflow-y-auto border rounded-md p-1 bg-white dark:bg-slate-900 text-xs">
            {#if importResults.details.length === 0}
              <p class="text-xs text-slate-500 dark:text-slate-400 p-2">无详细信息</p>
            {:else}
              <ul class="divide-y divide-slate-100 dark:divide-slate-800">
                {#each importResults.details as detail}
                  <li class="p-1 flex items-start">
                    {#if detail.includes('成功')}
                      <CheckCircle2 class="h-3 w-3 text-green-500 dark:text-green-400 mr-1 mt-0.5 flex-shrink-0" />
                      <span class="text-slate-700 dark:text-slate-300">{detail}</span>
                    {:else if detail.includes('失败')}
                      <AlertCircle class="h-3 w-3 text-red-500 dark:text-red-400 mr-1 mt-0.5 flex-shrink-0" />
                      <span class="text-red-600 dark:text-red-400">{detail}</span>
                    {:else}
                      <span class="text-slate-600 dark:text-slate-400 ml-4">{detail}</span>
                    {/if}
                  </li>
                {/each}
              </ul>
            {/if}
          </div>
        </div>
      </div>

      <Dialog.Footer class="pt-2 border-t">
        <Button variant="default" onclick={() => showImportResultDialog = false} class="min-w-[80px]">
          关闭
        </Button>
      </Dialog.Footer>
    </Dialog.Content>
  </Dialog.Root>
</div>
