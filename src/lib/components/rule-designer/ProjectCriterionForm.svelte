<script lang="ts">
  import { onMount } from 'svelte';
  import { Button } from '$lib/components/ui/button';
  import { ruleDesignerService, type RuleDefinition, type ProjectCriterionWithRule, type CreateProjectCriterionRequest, type UpdateProjectCriterionRequest } from '$lib/services/ruleDesignerService';
  import { Save } from 'lucide-svelte';

  // Props
  const {
    projectId,
    criterionType = 'inclusion',
    projectCriterion = null,
    onSuccess = undefined
  } = $props<{
    projectId: string;
    criterionType?: 'inclusion' | 'exclusion';
    projectCriterion?: ProjectCriterionWithRule | null;
    onSuccess?: () => void;
  }>();

  // 状态管理
  let isLoading = $state(true);
  let isSubmitting = $state(false);
  let error = $state<string | null>(null);
  let success = $state<string | null>(null);
  let ruleDefinitions = $state<RuleDefinition[]>([]);
  let selectedRuleDefinition = $state<RuleDefinition | null>(null);
  let parameterValues = $state<Record<string, any>>({});

  // 加载规则定义
  async function loadRuleDefinitions() {
    isLoading = true;
    error = null;

    try {
      ruleDefinitions = await ruleDesignerService.getRuleDefinitions();
    } catch (err: any) {
      error = err.message || '加载规则定义失败';
      console.error('加载规则定义失败:', err);
    } finally {
      isLoading = false;
    }
  }

  // 选择规则定义
  function selectRuleDefinition(ruleDefinitionId: number) {
    const rule = ruleDefinitions.find(r => r.rule_definition_id === ruleDefinitionId);
    if (rule) {
      selectedRuleDefinition = rule;
      // 重置参数值
      parameterValues = {};
    }
  }

  // 获取参数模式
  function getParameterSchema() {
    if (!selectedRuleDefinition) return { parameters: [] };

    try {
      const schema = ruleDesignerService.parseParameterSchema(selectedRuleDefinition.parameter_schema);
      console.log('[ProjectCriterionForm] 解析参数模式:', schema);

      // 检查参数是否有readonly属性
      if (schema.parameters) {
        schema.parameters.forEach(param => {
          if (param.readonly) {
            console.warn(`[ProjectCriterionForm] 参数 ${param.name} 被设置为只读:`, param);
          }
        });
      }

      return schema;
    } catch (e) {
      console.error('解析参数模式失败:', e);
      return { parameters: [] };
    }
  }

  // 更新参数值
  function updateParameterValue(name: string, value: any) {
    parameterValues = { ...parameterValues, [name]: value };
  }

  // 验证表单
  function validateForm(): boolean {
    if (!selectedRuleDefinition) {
      error = '请选择规则定义';
      return false;
    }

    const schema = getParameterSchema();

    // 检查必填参数
    for (const param of schema.parameters) {
      if (param.required && (parameterValues[param.name] === undefined || parameterValues[param.name] === null || parameterValues[param.name] === '')) {
        error = `参数 "${param.label}" 是必填项`;
        return false;
      }
    }

    return true;
  }

  // 提交表单
  async function handleSubmit(e: Event) {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    isSubmitting = true;
    error = null;
    success = null;

    try {
      const parameterValuesJson = ruleDesignerService.stringifyParameterValues(parameterValues);

      if (projectCriterion) {
        // 更新
        const updateRequest: UpdateProjectCriterionRequest = {
          rule_definition_id: selectedRuleDefinition!.rule_definition_id,
          criterion_type: criterionType,
          parameter_values: parameterValuesJson
        };

        await ruleDesignerService.updateProjectCriterion(
          projectCriterion.criterion.project_criterion_id!,
          updateRequest
        );

        success = '项目标准更新成功';

        // 如果提供了成功回调，调用它
        if (onSuccess) {
          console.log('[ProjectCriterionForm] Calling onSuccess callback after update');
          onSuccess();
        }
      } else {
        // 创建
        const createRequest: CreateProjectCriterionRequest = {
          project_id: projectId,
          rule_definition_id: selectedRuleDefinition!.rule_definition_id!,
          criterion_type: criterionType,
          parameter_values: parameterValuesJson
        };

        await ruleDesignerService.createProjectCriterion(createRequest);

        success = '项目标准创建成功';

        // 如果提供了成功回调，调用它
        if (onSuccess) {
          console.log('[ProjectCriterionForm] Calling onSuccess callback after create');
          onSuccess();
        } else {
          // 如果没有回调，重置表单以便继续添加
          selectedRuleDefinition = null;
          parameterValues = {};
        }
      }
    } catch (err: any) {
      error = err.message || (projectCriterion ? '更新项目标准失败' : '创建项目标准失败');
      console.error(projectCriterion ? '更新项目标准失败:' : '创建项目标准失败:', err);
    } finally {
      isSubmitting = false;
    }
  }

  // 组件挂载时初始化
  onMount(async () => {
    try {
      // 确保规则设计器表已初始化
      await ruleDesignerService.initTables();

      // 加载规则定义
      await loadRuleDefinitions();

      // 如果是编辑模式，使用传入的数据初始化表单
      if (projectCriterion) {
        console.log('[ProjectCriterionForm] Editing criterion:', projectCriterion);

        // 设置选中的规则定义
        selectedRuleDefinition = projectCriterion.rule_definition;
        console.log('[ProjectCriterionForm] Selected rule definition:', selectedRuleDefinition);

        try {
          // 解析参数值
          const parsedValues = ruleDesignerService.parseParameterValues(projectCriterion.criterion.parameter_values);
          console.log('[ProjectCriterionForm] Parsed parameter values:', parsedValues);
          parameterValues = parsedValues;
        } catch (e) {
          console.error('[ProjectCriterionForm] Failed to parse parameter values:', e);
          error = '解析参数值失败，请重新编辑';
          parameterValues = {};
        }
      }
    } catch (err: any) {
      console.error('[ProjectCriterionForm] Initialization error:', err);
      error = err.message || '初始化失败';
    } finally {
      isLoading = false;
    }
  });
</script>

<div class="w-full" style="pointer-events: auto; position: relative; z-index: auto;">
  {#if error}
    <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
      <span class="block sm:inline">{error}</span>
    </div>
  {/if}

  {#if success}
    <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
      <span class="block sm:inline">{success}</span>
    </div>
  {/if}

  {#if isLoading}
    <div class="flex justify-center items-center py-12">
      <div class="inline-block w-6 h-6 border-2 border-t-transparent border-blue-600 rounded-full animate-spin"></div>
      <span class="ml-2 text-slate-600 dark:text-slate-300">加载中...</span>
    </div>
  {:else}
    <form onsubmit={handleSubmit} class="space-y-6">
      <!-- 规则定义选择 -->
      <div>
        <label for="rule-definition" class="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-1">
          规则定义 <span class="text-red-500">*</span>
        </label>
        <select
          id="rule-definition"
          value={selectedRuleDefinition?.rule_definition_id || ''}
          onchange={(e) => selectRuleDefinition(parseInt(e.currentTarget.value))}
          class="w-full px-3 py-2 border border-slate-300 dark:border-slate-600 rounded-md text-sm text-slate-900 dark:text-slate-100 bg-white dark:bg-slate-800 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-600"
          disabled={!!projectCriterion}
        >
          <option value="">请选择规则定义</option>
          {#each ruleDefinitions as rule}
            <option value={rule.rule_definition_id}>{rule.rule_name} {rule.category ? `(${rule.category})` : ''}</option>
          {/each}
        </select>
      </div>

      {#if selectedRuleDefinition}
        <!-- 规则描述 -->
        {#if selectedRuleDefinition.rule_description}
          <div class="bg-slate-50 dark:bg-slate-800 p-4 rounded-md">
            <h3 class="text-sm font-medium text-slate-700 dark:text-slate-300 mb-1">规则描述</h3>
            <p class="text-sm text-slate-600 dark:text-slate-400">{selectedRuleDefinition.rule_description}</p>
          </div>
        {/if}

        <!-- 参数表单 -->
        <div class="border-2 border-blue-200 dark:border-blue-700 rounded-lg p-5 bg-blue-50/30 dark:bg-blue-900/10 shadow-md">
          <h3 class="text-lg font-semibold mb-4 text-blue-800 dark:text-blue-300 flex items-center">
            <span class="bg-blue-100 p-1 rounded-md mr-2">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
              </svg>
            </span>
            参数配置
          </h3>

          {#if getParameterSchema().parameters.length === 0}
            <p class="text-sm text-slate-500 dark:text-slate-400">此规则没有可配置的参数</p>
          {:else}
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              {#each getParameterSchema().parameters as param}
                <div>
                  <label for={param.name} class="block text-sm font-semibold text-blue-700 dark:text-blue-300 mb-2">
                    {param.label}
                    {#if param.required}
                      <span class="text-red-500 ml-0.5">*</span>
                    {/if}
                    {#if param.unit}
                      <span class="bg-blue-100 text-blue-800 px-1.5 py-0.5 rounded-full text-xs ml-1">
                        {param.unit}
                      </span>
                    {/if}
                  </label>

                  {#if param.type === 'string'}
                    <input
                      type="text"
                      id={param.name}
                      value={parameterValues[param.name] || ''}
                      oninput={(e) => updateParameterValue(param.name, e.currentTarget.value)}
                      onclick={(e) => {
                        console.log(`[ProjectCriterionForm] 点击输入框: ${param.name}, readonly: ${param.readonly}`);
                        if (!param.readonly) {
                          e.currentTarget.focus();
                        }
                      }}
                      onfocus={() => {
                        console.log(`[ProjectCriterionForm] 输入框获得焦点: ${param.name}`);
                      }}
                      class="w-full px-4 py-3 border-2 border-blue-200 dark:border-blue-600 rounded-lg text-base text-slate-900 dark:text-slate-100 bg-white dark:bg-slate-800 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:focus:ring-blue-600 shadow-sm"
                      placeholder={`请输入${param.label}`}
                      readonly={param.readonly || false}
                      disabled={param.readonly || false}
                      tabindex={param.readonly ? -1 : 0}
                      style="pointer-events: auto; position: relative; z-index: 1;"
                    />
                  {:else if param.type === 'integer'}
                    <input
                      type="number"
                      id={param.name}
                      value={parameterValues[param.name] || ''}
                      oninput={(e) => updateParameterValue(param.name, parseInt(e.currentTarget.value) || null)}
                      class="w-full px-4 py-3 border-2 border-blue-200 dark:border-blue-600 rounded-lg text-base text-slate-900 dark:text-slate-100 bg-white dark:bg-slate-800 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:focus:ring-blue-600 shadow-sm"
                      placeholder={`请输入${param.label}`}
                      step="1"
                      readonly={param.readonly || false}
                      disabled={param.readonly || false}
                      tabindex={param.readonly ? -1 : 0}
                    />
                  {:else if param.type === 'number'}
                    <input
                      type="number"
                      id={param.name}
                      value={parameterValues[param.name] || ''}
                      oninput={(e) => updateParameterValue(param.name, parseFloat(e.currentTarget.value) || null)}
                      class="w-full px-4 py-3 border-2 border-blue-200 dark:border-blue-600 rounded-lg text-base text-slate-900 dark:text-slate-100 bg-white dark:bg-slate-800 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:focus:ring-blue-600 shadow-sm"
                      placeholder={`请输入${param.label}`}
                      step="any"
                      readonly={param.readonly || false}
                      disabled={param.readonly || false}
                      tabindex={param.readonly ? -1 : 0}
                    />
                  {:else if param.type === 'boolean'}
                    <div class="flex items-center">
                      <input
                        type="checkbox"
                        id={param.name}
                        checked={parameterValues[param.name] || false}
                        onchange={(e) => updateParameterValue(param.name, e.currentTarget.checked)}
                        class="rounded border-slate-300 dark:border-slate-600 text-blue-600 focus:ring-blue-500 dark:focus:ring-blue-600"
                        disabled={param.readonly}
                      />
                      <label for={param.name} class="ml-2 text-sm text-slate-700 dark:text-slate-300">
                        {param.label}
                      </label>
                    </div>
                  {:else if param.type === 'enum' && param.options}
                    <select
                      id={param.name}
                      value={parameterValues[param.name] || ''}
                      onchange={(e) => updateParameterValue(param.name, e.currentTarget.value)}
                      class="w-full px-4 py-3 border-2 border-blue-200 dark:border-blue-600 rounded-lg text-base text-slate-900 dark:text-slate-100 bg-white dark:bg-slate-800 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:focus:ring-blue-600 shadow-sm"
                      disabled={param.readonly}
                    >
                      <option value="">请选择{param.label}</option>
                      {#each param.options as option}
                        <option value={option}>{option}</option>
                      {/each}
                    </select>
                  {/if}
                </div>
              {/each}
            </div>
          {/if}
        </div>
      {/if}

      <!-- 提交按钮 -->
      <div class="flex justify-end">
        <Button type="submit" disabled={isSubmitting || !selectedRuleDefinition} class="flex items-center space-x-2">
          {#if isSubmitting}
            <span class="inline-block w-4 h-4 border-2 border-t-transparent border-current rounded-full animate-spin"></span>
          {/if}
          <Save class="h-4 w-4" />
          <span>{projectCriterion ? '更新标准' : '保存标准'}</span>
        </Button>
      </div>
    </form>
  {/if}
</div>
